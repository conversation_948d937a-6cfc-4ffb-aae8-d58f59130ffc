package xiaozhi.modules.config.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import lombok.AllArgsConstructor;
import xiaozhi.common.constant.Constant;
import xiaozhi.common.exception.ErrorCode;
import xiaozhi.common.exception.RenException;
import xiaozhi.common.redis.RedisKeys;
import xiaozhi.common.redis.RedisUtils;
import xiaozhi.common.utils.JsonUtils;
import xiaozhi.modules.agent.entity.AgentEntity;
import xiaozhi.modules.agent.entity.AgentTemplateEntity;
import xiaozhi.modules.agent.service.AgentService;
import xiaozhi.modules.agent.service.AgentTemplateService;
import xiaozhi.modules.config.service.ConfigService;
import xiaozhi.modules.device.entity.DeviceEntity;
import xiaozhi.modules.device.service.DeviceService;
import xiaozhi.modules.device.service.MacAddressService;
import xiaozhi.modules.model.entity.ModelConfigEntity;
import xiaozhi.modules.model.service.ModelConfigService;
import xiaozhi.modules.sys.dto.SysParamsDTO;
import xiaozhi.modules.sys.service.SysParamsService;
import xiaozhi.modules.timbre.service.TimbreService;
import xiaozhi.modules.timbre.vo.TimbreDetailsVO;

@Service
@AllArgsConstructor
public class ConfigServiceImpl implements ConfigService {
    private static final Logger log = LoggerFactory.getLogger(ConfigServiceImpl.class);

    private final SysParamsService sysParamsService;
    private final DeviceService deviceService;
    private final ModelConfigService modelConfigService;
    private final AgentService agentService;
    private final AgentTemplateService agentTemplateService;
    private final RedisUtils redisUtils;
    private final TimbreService timbreService;
    private final MacAddressService macAddressService;

    @Override
    public Object getConfig(Boolean isCache) {
        if (isCache) {
            // 先从Redis获取配置
            Object cachedConfig = redisUtils.get(RedisKeys.getServerConfigKey());
            if (cachedConfig != null) {
                return cachedConfig;
            }
        }

        // 构建配置信息
        Map<String, Object> result = new HashMap<>();
        buildConfig(result);

        // 查询默认智能体
        AgentTemplateEntity agent = agentTemplateService.getDefaultTemplate();
        if (agent == null) {
            throw new RenException("默认智能体未找到");
        }

        // 构建模块配置
        buildModuleConfig(
                null,
                null,
                null,
                null,
                agent.getVadModelId(),
                agent.getAsrModelId(),
                null,
                null,
                null,
                null,
                result,
                isCache);

        // 将配置存入Redis
        redisUtils.set(RedisKeys.getServerConfigKey(), result);

        return result;
    }

    @Override
    public Map<String, Object> getAgentModels(String macAddress, Map<String, String> selectedModule) {
        log.info("开始获取设备配置，MAC地址: {}", macAddress);

        // 根据MAC地址查找设备
        DeviceEntity device = deviceService.getDeviceByMacAddress(macAddress);
        if (device == null) {
            log.info("设备表中未找到MAC地址: {}，检查MAC白名单", macAddress);

            // 检查MAC地址是否在白名单中（MAC认证设备）
            boolean isMacEnabled = macAddressService.checkMacAddressEnabled(macAddress);
            log.info("MAC地址 {} 白名单检查结果: {}", macAddress, isMacEnabled);

            if (isMacEnabled) {
                log.info("MAC认证设备 {} 使用默认智能体配置", macAddress);
                // MAC认证设备，使用默认智能体配置
                Map<String, Object> defaultConfig = getDefaultAgentModelsForMacDevice(macAddress, selectedModule);
                log.info("MAC认证设备 {} 默认配置获取成功，配置项数量: {}", macAddress, defaultConfig.size());
                log.debug("MAC认证设备 {} 默认配置详情: {}", macAddress, defaultConfig);
                return defaultConfig;
            }

            log.warn("MAC地址 {} 不在白名单中，检查Redis缓存", macAddress);
            // 如果设备不存在且不在MAC白名单中，去redis里看看有没有需要连接的设备
            String cachedCode = deviceService.geCodeByDeviceId(macAddress);
            if (StringUtils.isNotBlank(cachedCode)) {
                log.info("设备 {} 需要绑定，绑定码: {}", macAddress, cachedCode);
                throw new RenException(ErrorCode.OTA_DEVICE_NEED_BIND, cachedCode);
            }
            log.error("设备 {} 未找到且不在MAC白名单中", macAddress);
            throw new RenException(ErrorCode.OTA_DEVICE_NOT_FOUND, "not found device");
        }

        log.info("在设备表中找到设备，MAC地址: {}，设备ID: {}", macAddress, device.getId());

        // 获取智能体信息
        AgentEntity agent = agentService.getAgentById(device.getAgentId());
        if (agent == null) {
            throw new RenException("智能体未找到");
        }
        // 获取音色信息
        String voice = null;
        TimbreDetailsVO timbre = timbreService.get(agent.getTtsVoiceId());
        if (timbre != null) {
            voice = timbre.getTtsVoice();
        }
        // 构建返回数据
        Map<String, Object> result = new HashMap<>();
        // 获取单台设备每天最多输出字数
        String deviceMaxOutputSize = sysParamsService.getValue("device_max_output_size", true);
        result.put("device_max_output_size", deviceMaxOutputSize);

        // 获取聊天记录配置
        Integer chatHistoryConf = agent.getChatHistoryConf();
        if (agent.getMemModelId() != null && agent.getMemModelId().equals(Constant.MEMORY_NO_MEM)) {
            chatHistoryConf = Constant.ChatHistoryConfEnum.IGNORE.getCode();
        } else if (agent.getMemModelId() != null
                && !agent.getMemModelId().equals(Constant.MEMORY_NO_MEM)
                && agent.getChatHistoryConf() == null) {
            chatHistoryConf = Constant.ChatHistoryConfEnum.RECORD_TEXT_AUDIO.getCode();
        }
        result.put("chat_history_conf", chatHistoryConf);
        // 如果客户端已实例化模型，则不返回
        String alreadySelectedVadModelId = (String) selectedModule.get("VAD");
        if (alreadySelectedVadModelId != null && alreadySelectedVadModelId.equals(agent.getVadModelId())) {
            agent.setVadModelId(null);
        }
        String alreadySelectedAsrModelId = (String) selectedModule.get("ASR");
        if (alreadySelectedAsrModelId != null && alreadySelectedAsrModelId.equals(agent.getAsrModelId())) {
            agent.setAsrModelId(null);
        }

        // 构建模块配置
        buildModuleConfig(
                agent.getAgentName(),
                agent.getSystemPrompt(),
                agent.getSummaryMemory(),
                voice,
                agent.getVadModelId(),
                agent.getAsrModelId(),
                agent.getLlmModelId(),
                agent.getTtsModelId(),
                agent.getMemModelId(),
                agent.getIntentModelId(),
                result,
                true);

        return result;
    }

    /**
     * 为MAC认证设备获取默认智能体配置
     *
     * @param macAddress MAC地址
     * @param selectedModule 已选择的模块
     * @return 默认智能体配置
     */
    private Map<String, Object> getDefaultAgentModelsForMacDevice(String macAddress, Map<String, String> selectedModule) {
        log.info("开始为MAC认证设备 {} 构建默认配置", macAddress);

        // 获取默认智能体模板
        AgentTemplateEntity defaultAgent = agentTemplateService.getDefaultTemplate();
        if (defaultAgent == null) {
            log.error("默认智能体模板未找到，MAC地址: {}", macAddress);
            throw new RenException("默认智能体模板未找到");
        }

        log.info("MAC认证设备 {} 使用默认智能体模板: {}, ID: {}", macAddress, defaultAgent.getAgentName(), defaultAgent.getId());
        log.info("MAC认证设备 {} 默认智能体详细信息 - 名称: {}, 编码: {}, 提示词前100字符: {}",
                macAddress, defaultAgent.getAgentName(), defaultAgent.getAgentCode(),
                defaultAgent.getSystemPrompt() != null ? defaultAgent.getSystemPrompt().substring(0, Math.min(100, defaultAgent.getSystemPrompt().length())) : "null");

        // 构建返回数据
        Map<String, Object> result = new HashMap<>();

        // 获取单台设备每天最多输出字数
        String deviceMaxOutputSize = sysParamsService.getValue("device_max_output_size", true);
        result.put("device_max_output_size", deviceMaxOutputSize);
        log.debug("MAC认证设备 {} 设置最大输出字数: {}", macAddress, deviceMaxOutputSize);

        // 获取聊天记录配置（使用默认配置）
        Integer chatHistoryConf = defaultAgent.getChatHistoryConf();
        if (defaultAgent.getMemModelId() != null && defaultAgent.getMemModelId().equals(Constant.MEMORY_NO_MEM)) {
            chatHistoryConf = Constant.ChatHistoryConfEnum.IGNORE.getCode();
        } else if (defaultAgent.getMemModelId() != null
                && !defaultAgent.getMemModelId().equals(Constant.MEMORY_NO_MEM)
                && defaultAgent.getChatHistoryConf() == null) {
            chatHistoryConf = Constant.ChatHistoryConfEnum.RECORD_TEXT_AUDIO.getCode();
        }
        result.put("chat_history_conf", chatHistoryConf);
        log.debug("MAC认证设备 {} 设置聊天记录配置: {}", macAddress, chatHistoryConf);

        // 如果客户端已实例化模型，则不返回
        String alreadySelectedVadModelId = (String) selectedModule.get("VAD");
        if (alreadySelectedVadModelId != null && alreadySelectedVadModelId.equals(defaultAgent.getVadModelId())) {
            defaultAgent.setVadModelId(null);
        }
        String alreadySelectedAsrModelId = (String) selectedModule.get("ASR");
        if (alreadySelectedAsrModelId != null && alreadySelectedAsrModelId.equals(defaultAgent.getAsrModelId())) {
            defaultAgent.setAsrModelId(null);
        }

        // 构建模块配置（使用默认智能体的配置）
        log.info("MAC认证设备 {} 开始构建模块配置，模型ID - VAD: {}, ASR: {}, LLM: {}, TTS: {}, Memory: {}, Intent: {}",
                macAddress, defaultAgent.getVadModelId(), defaultAgent.getAsrModelId(),
                defaultAgent.getLlmModelId(), defaultAgent.getTtsModelId(),
                defaultAgent.getMemModelId(), defaultAgent.getIntentModelId());

        buildModuleConfig(
                defaultAgent.getAgentName(),
                defaultAgent.getSystemPrompt(),
                defaultAgent.getSummaryMemory(),
                null, // MAC认证设备使用默认音色
                defaultAgent.getVadModelId(),
                defaultAgent.getAsrModelId(),
                defaultAgent.getLlmModelId(),
                defaultAgent.getTtsModelId(),
                defaultAgent.getMemModelId(),
                defaultAgent.getIntentModelId(),
                result,
                true);

        log.info("MAC认证设备 {} 模块配置构建完成，最终配置项数量: {}", macAddress, result.size());
        log.debug("MAC认证设备 {} 最终配置内容: {}", macAddress, result);

        return result;
    }

    /**
     * 构建配置信息
     *
     * @param paramsList 系统参数列表
     * @return 配置信息
     */
    private Object buildConfig(Map<String, Object> config) {

        // 查询所有系统参数
        List<SysParamsDTO> paramsList = sysParamsService.list(new HashMap<>());

        for (SysParamsDTO param : paramsList) {
            String[] keys = param.getParamCode().split("\\.");
            Map<String, Object> current = config;

            // 遍历除最后一个key之外的所有key
            for (int i = 0; i < keys.length - 1; i++) {
                String key = keys[i];
                if (!current.containsKey(key)) {
                    current.put(key, new HashMap<String, Object>());
                }
                current = (Map<String, Object>) current.get(key);
            }

            // 处理最后一个key
            String lastKey = keys[keys.length - 1];
            String value = param.getParamValue();

            // 根据valueType转换值
            switch (param.getValueType().toLowerCase()) {
                case "number":
                    try {
                        double doubleValue = Double.parseDouble(value);
                        // 如果数值是整数形式，则转换为Integer
                        if (doubleValue == (int) doubleValue) {
                            current.put(lastKey, (int) doubleValue);
                        } else {
                            current.put(lastKey, doubleValue);
                        }
                    } catch (NumberFormatException e) {
                        current.put(lastKey, value);
                    }
                    break;
                case "boolean":
                    current.put(lastKey, Boolean.parseBoolean(value));
                    break;
                case "array":
                    // 将分号分隔的字符串转换为数字数组
                    List<String> list = new ArrayList<>();
                    for (String num : value.split(";")) {
                        if (StringUtils.isNotBlank(num)) {
                            list.add(num.trim());
                        }
                    }
                    current.put(lastKey, list);
                    break;
                case "json":
                    try {
                        current.put(lastKey, JsonUtils.parseObject(value, Object.class));
                    } catch (Exception e) {
                        current.put(lastKey, value);
                    }
                    break;
                default:
                    current.put(lastKey, value);
            }
        }

        return config;
    }

    /**
     * 构建模块配置
     *
     * @param prompt        提示词
     * @param voice         音色
     * @param vadModelId    VAD模型ID
     * @param asrModelId    ASR模型ID
     * @param llmModelId    LLM模型ID
     * @param ttsModelId    TTS模型ID
     * @param memModelId    记忆模型ID
     * @param intentModelId 意图模型ID
     * @param result        结果Map
     */
    private void buildModuleConfig(
            String assistantName,
            String prompt,
            String summaryMemory,
            String voice,
            String vadModelId,
            String asrModelId,
            String llmModelId,
            String ttsModelId,
            String memModelId,
            String intentModelId,
            Map<String, Object> result,
            boolean isCache) {
        Map<String, String> selectedModule = new HashMap<>();

        String[] modelTypes = { "VAD", "ASR", "TTS", "Memory", "Intent", "LLM" };
        String[] modelIds = { vadModelId, asrModelId, ttsModelId, memModelId, intentModelId, llmModelId };
        String intentLLMModelId = null;
        String memLocalShortLLMModelId = null;

        for (int i = 0; i < modelIds.length; i++) {
            if (modelIds[i] == null) {
                continue;
            }
            ModelConfigEntity model = modelConfigService.getModelById(modelIds[i], isCache);
            Map<String, Object> typeConfig = new HashMap<>();
            if (model.getConfigJson() != null) {
                typeConfig.put(model.getId(), model.getConfigJson());
                // 如果是TTS类型，添加private_voice属性
                if ("TTS".equals(modelTypes[i]) && voice != null) {
                    ((Map<String, Object>) model.getConfigJson()).put("private_voice", voice);
                }
                // 如果是Intent类型，且type=intent_llm，则给他添加附加模型
                if ("Intent".equals(modelTypes[i])) {
                    Map<String, Object> map = (Map<String, Object>) model.getConfigJson();
                    if ("intent_llm".equals(map.get("type"))) {
                        intentLLMModelId = (String) map.get("llm");
                        if (StringUtils.isNotBlank(intentLLMModelId) && intentLLMModelId.equals(llmModelId)) {
                            intentLLMModelId = null;
                        }
                    }
                    if (map.get("functions") != null) {
                        String functionStr = (String) map.get("functions");
                        if (StringUtils.isNotBlank(functionStr)) {
                            String[] functions = functionStr.split("\\;");
                            map.put("functions", functions);
                        }
                    }
                }
                if ("Memory".equals(modelTypes[i])) {
                    Map<String, Object> map = (Map<String, Object>) model.getConfigJson();
                    if ("mem_local_short".equals(map.get("type"))) {
                        memLocalShortLLMModelId = (String) map.get("llm");
                        if (StringUtils.isNotBlank(memLocalShortLLMModelId)
                                && memLocalShortLLMModelId.equals(llmModelId)) {
                            memLocalShortLLMModelId = null;
                        }
                    }
                }
                // 如果是LLM类型，且intentLLMModelId不为空，则添加附加模型
                if ("LLM".equals(modelTypes[i])) {
                    if (StringUtils.isNotBlank(intentLLMModelId)) {
                        if (!typeConfig.containsKey(intentLLMModelId)) {
                            ModelConfigEntity intentLLM = modelConfigService.getModelById(intentLLMModelId, isCache);
                            typeConfig.put(intentLLM.getId(), intentLLM.getConfigJson());
                        }
                    }
                    if (StringUtils.isNotBlank(memLocalShortLLMModelId)) {
                        if (!typeConfig.containsKey(memLocalShortLLMModelId)) {
                            ModelConfigEntity memLocalShortLLM = modelConfigService
                                    .getModelById(memLocalShortLLMModelId, isCache);
                            typeConfig.put(memLocalShortLLM.getId(), memLocalShortLLM.getConfigJson());
                        }
                    }
                }
            }
            result.put(modelTypes[i], typeConfig);

            selectedModule.put(modelTypes[i], model.getId());
        }

        result.put("selected_module", selectedModule);

        log.info("构建模块配置 - 智能体名称: {}, 原始提示词前100字符: {}",
                assistantName, prompt != null ? prompt.substring(0, Math.min(100, prompt.length())) : "null");

        if (StringUtils.isNotBlank(prompt)) {
            String finalAssistantName = StringUtils.isBlank(assistantName) ? "小智" : assistantName;
            log.info("替换智能体名称 - 使用名称: {}, 是否为默认值: {}", finalAssistantName, StringUtils.isBlank(assistantName));
            prompt = prompt.replace("{{assistant_name}}", finalAssistantName);
        }

        log.info("最终提示词前100字符: {}", prompt != null ? prompt.substring(0, Math.min(100, prompt.length())) : "null");

        result.put("prompt", prompt);
        result.put("summaryMemory", summaryMemory);
    }
}
