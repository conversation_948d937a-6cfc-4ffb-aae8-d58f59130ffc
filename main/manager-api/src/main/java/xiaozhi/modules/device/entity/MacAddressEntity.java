package xiaozhi.modules.device.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * MAC地址白名单实体
 *
 * <AUTHOR>
 * @version 1.0, 2025/6/15
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ai_mac_whitelist")
@Schema(description = "MAC地址白名单")
public class MacAddressEntity {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "ID")
    private Long id;

    /**
     * MAC地址
     */
    @Schema(description = "MAC地址")
    private String macAddress;

    /**
     * 设备备注
     */
    @Schema(description = "设备备注")
    private String remark;

    /**
     * 状态(0禁用/1启用)
     */
    @Schema(description = "状态(0禁用/1启用)")
    private Integer status;

    /**
     * 是否已绑定设备(0未绑定/1已绑定)
     */
    @Schema(description = "是否已绑定设备(0未绑定/1已绑定)")
    private Integer bound;

    /**
     * 关联设备ID - 逻辑外键关联ai_device表的id字段
     */
    @Schema(description = "关联设备ID - 逻辑外键关联ai_device表的id字段")
    private String deviceId;

    /**
     * 注册来源(manual:手动导入, auto:自动注册, batch:批量导入)
     */
    @Schema(description = "注册来源(manual:手动导入, auto:自动注册, batch:批量导入)")
    private String registerSource;

    /**
     * 注册时间
     */
    @Schema(description = "注册时间")
    private Date registerTime;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建者")
    private Long creator;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private Date createDate;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新者")
    private Long updater;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新时间")
    private Date updateDate;
}
