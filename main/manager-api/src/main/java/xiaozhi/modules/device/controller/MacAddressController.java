package xiaozhi.modules.device.controller;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import xiaozhi.common.annotation.LogOperation;
import xiaozhi.common.page.PageData;
import xiaozhi.common.utils.Result;
import xiaozhi.common.validator.ValidatorUtils;
import xiaozhi.common.validator.group.AddGroup;
import xiaozhi.common.validator.group.DefaultGroup;
import xiaozhi.common.validator.group.UpdateGroup;
import xiaozhi.modules.device.dto.MacAddressDTO;
import xiaozhi.modules.device.dto.MacAutoRegisterDTO;
import xiaozhi.modules.device.service.MacAddressService;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * MAC地址白名单管理
 *
 * <AUTHOR>
 * @version 1.0, 2025/6/15
 * @since 1.0.0
 */
@RestController
@RequestMapping("device/mac")
@Tag(name = "MAC地址白名单管理")
@AllArgsConstructor
public class MacAddressController {

    private final MacAddressService macAddressService;

    @GetMapping("page")
    @Operation(summary = "分页查询")
    @RequiresPermissions("sys:role:admin")
    public Result<PageData<MacAddressDTO>> page(@RequestParam Map<String, Object> params) {
        PageData<MacAddressDTO> page = macAddressService.page(params);
        return new Result<PageData<MacAddressDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @Operation(summary = "获取详情")
    @RequiresPermissions("sys:role:admin")
    public Result<MacAddressDTO> get(@PathVariable("id") Long id) {
        MacAddressDTO data = macAddressService.get(id);
        return new Result<MacAddressDTO>().ok(data);
    }

    @PostMapping
    @Operation(summary = "保存")
    @LogOperation("保存MAC地址")
    @RequiresPermissions("sys:role:admin")
    public Result<Void> save(@RequestBody MacAddressDTO dto) {
        // 效验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        macAddressService.save(dto);

        return new Result<>();
    }

    @PutMapping
    @Operation(summary = "修改")
    @LogOperation("修改MAC地址")
    @RequiresPermissions("sys:role:admin")
    public Result<Void> update(@RequestBody MacAddressDTO dto) {
        // 效验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        macAddressService.update(dto);

        return new Result<>();
    }

    @DeleteMapping
    @Operation(summary = "删除")
    @LogOperation("删除MAC地址")
    @RequiresPermissions("sys:role:admin")
    public Result<Void> delete(@RequestBody Long[] ids) {
        macAddressService.delete(ids);

        return new Result<>();
    }

    @PostMapping("import")
    @Operation(summary = "批量导入")
    @LogOperation("批量导入MAC地址")
    @RequiresPermissions("sys:role:admin")
    public Result<Integer> importMacAddresses(@RequestParam("file") MultipartFile file) {
        try {
            List<String> macAddresses = new ArrayList<>();

            try (BufferedReader reader = new BufferedReader(new InputStreamReader(file.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    macAddresses.add(line.trim());
                }
            }

            int successCount = macAddressService.batchImport(macAddresses);

            return new Result<Integer>().ok(successCount);
        } catch (Exception e) {
            return new Result<Integer>().error("导入失败：" + e.getMessage());
        }
    }

    @GetMapping("maclist")
    @Operation(summary = "获取所有启用的MAC地址列表")
    public Result<List<String>> getAllEnabledMacAddresses() {
        List<String> macAddresses = macAddressService.getAllEnabledMacAddresses();
        return new Result<List<String>>().ok(macAddresses);
    }

    @GetMapping("blacklist")
    @Operation(summary = "获取所有黑名单MAC地址列表")
    public Result<List<String>> getAllBlacklistMacAddresses() {
        List<String> macAddresses = macAddressService.getAllBlacklistMacAddresses();
        return new Result<List<String>>().ok(macAddresses);
    }

    @PostMapping("auto-register")
    @Operation(summary = "自动注册MAC地址到白名单")
    public Result<Boolean> autoRegisterMacAddress(@RequestBody MacAutoRegisterDTO dto) {
        // 效验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        boolean success = macAddressService.autoRegisterMacAddress(dto.getMacAddress(), dto.getRemark());

        if (success) {
            return new Result<Boolean>().ok(true);
        } else {
            return new Result<Boolean>().error("MAC地址自动注册失败，可能已存在或格式不正确");
        }
    }
}
