package xiaozhi.modules.device.service;

import xiaozhi.common.page.PageData;
import xiaozhi.common.service.BaseService;
import xiaozhi.modules.device.dto.MacAddressDTO;
import xiaozhi.modules.device.entity.MacAddressEntity;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * MAC地址白名单服务接口
 *
 * <AUTHOR>
 * @version 1.0, 2025/6/15
 * @since 1.0.0
 */
public interface MacAddressService extends BaseService<MacAddressEntity> {

    /**
     * 分页查询
     *
     * @param params 查询参数
     * @return 分页数据
     */
    PageData<MacAddressDTO> page(Map<String, Object> params);

    /**
     * 获取详情
     *
     * @param id ID
     * @return 详情
     */
    MacAddressDTO get(Long id);

    /**
     * 保存
     *
     * @param dto DTO
     */
    void save(MacAddressDTO dto);

    /**
     * 更新
     *
     * @param dto DTO
     */
    void update(MacAddressDTO dto);

    /**
     * 删除
     *
     * @param ids ID数组
     */
    void delete(Long[] ids);

    /**
     * 获取所有启用的MAC地址列表
     *
     * @return MAC地址列表
     */
    List<String> getAllEnabledMacAddresses();

    /**
     * 分页获取启用的MAC地址列表
     *
     * @param page 页码
     * @param pageSize 每页数量
     * @return MAC地址列表
     */
    List<String> getEnabledMacAddressesByPage(int page, int pageSize);

    /**
     * 获取启用的MAC地址总数
     *
     * @return MAC地址总数
     */
    int getEnabledMacAddressCount();

    /**
     * 获取所有黑名单MAC地址列表
     *
     * @return MAC地址列表
     */
    List<String> getAllBlacklistMacAddresses();

    /**
     * 分页获取黑名单MAC地址列表
     *
     * @param page 页码
     * @param pageSize 每页数量
     * @return MAC地址列表
     */
    List<String> getBlacklistMacAddressesByPage(int page, int pageSize);

    /**
     * 获取黑名单MAC地址总数
     *
     * @return MAC地址总数
     */
    int getBlacklistMacAddressCount();

    /**
     * 检查MAC地址是否存在
     *
     * @param macAddress MAC地址
     * @return 是否存在
     */
    boolean checkMacAddressExists(String macAddress);

    /**
     * 检查MAC地址是否启用
     *
     * @param macAddress MAC地址
     * @return 是否启用
     */
    boolean checkMacAddressEnabled(String macAddress);

    /**
     * 批量检查MAC地址是否启用
     *
     * @param macAddresses MAC地址列表
     * @return 启用的MAC地址列表
     */
    List<String> batchCheckMacAddressesEnabled(List<String> macAddresses);

    /**
     * 批量检查MAC地址是否在黑名单中
     *
     * @param macAddresses MAC地址列表
     * @return 黑名单中的MAC地址列表
     */
    List<String> batchCheckMacAddressesInBlacklist(List<String> macAddresses);

    /**
     * 批量导入MAC地址
     *
     * @param macAddresses MAC地址列表
     * @return 成功导入数量
     */
    int batchImport(List<String> macAddresses);

    /**
     * 使MAC地址缓存失效
     *
     * @param macAddress MAC地址
     */
    void invalidateCache(String macAddress);

    /**
     * 使所有MAC地址缓存失效
     */
    void invalidateAllCache();

    /**
     * 将MAC地址列表加载到Redis缓存
     */
    void loadMacAddressesToCache();

    /**
     * 从Redis缓存获取所有启用的MAC地址
     *
     * @return MAC地址集合
     */
    Set<String> getEnabledMacAddressesFromCache();

    /**
     * 从Redis缓存获取所有黑名单MAC地址
     *
     * @return MAC地址集合
     */
    Set<String> getBlacklistMacAddressesFromCache();

    /**
     * 检查MAC地址是否在缓存中
     *
     * @param macAddress MAC地址
     * @return 是否在缓存中
     */
    boolean isMacAddressInCache(String macAddress);

    /**
     * 检查MAC地址是否在黑名单缓存中
     *
     * @param macAddress MAC地址
     * @return 是否在黑名单缓存中
     */
    boolean isMacAddressInBlacklistCache(String macAddress);

    /**
     * 自动注册MAC地址到白名单
     *
     * @param macAddress MAC地址
     * @param remark 设备备注
     * @return 是否注册成功
     */
    boolean autoRegisterMacAddress(String macAddress, String remark);
}
