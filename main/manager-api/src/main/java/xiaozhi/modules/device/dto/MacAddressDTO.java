package xiaozhi.modules.device.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

import java.util.Date;

/**
 * MAC地址DTO
 *
 * <AUTHOR>
 * @version 1.0, 2025/6/15
 * @since 1.0.0
 */
@Data
@Schema(description = "MAC地址")
public class MacAddressDTO {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "MAC地址")
    @NotBlank(message = "MAC地址不能为空")
    @Pattern(regexp = "^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$", message = "MAC地址格式不正确")
    private String macAddress;

    @Schema(description = "设备备注")
    private String remark;

    @Schema(description = "状态(0禁用/1启用)")
    private Integer status;

    @Schema(description = "是否已绑定设备(0未绑定/1已绑定)")
    private Integer bound;

    @Schema(description = "关联设备ID")
    private String deviceId;

    @Schema(description = "注册来源(manual:手动导入, auto:自动注册, batch:批量导入)")
    private String registerSource;

    @Schema(description = "注册时间")
    private Date registerTime;
}
