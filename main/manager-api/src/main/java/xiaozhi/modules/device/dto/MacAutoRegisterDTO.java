package xiaozhi.modules.device.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * MAC地址自动注册DTO
 *
 * <AUTHOR>
 * @version 1.0, 2025/1/15
 * @since 1.0.0
 */
@Data
@Schema(description = "MAC地址自动注册")
public class MacAutoRegisterDTO {
    
    @Schema(description = "MAC地址")
    @NotBlank(message = "MAC地址不能为空")
    @Pattern(regexp = "^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$", message = "MAC地址格式不正确")
    private String macAddress;
    
    @Schema(description = "设备备注")
    private String remark;
}
