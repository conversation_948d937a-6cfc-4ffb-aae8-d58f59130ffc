package xiaozhi.modules.device.controller;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import xiaozhi.common.redis.RedisKeys;
import xiaozhi.common.redis.RedisUtils;
import xiaozhi.common.utils.Result;
import xiaozhi.modules.device.service.MacAddressService;
import xiaozhi.modules.quota.dao.MacBlacklistDao;
import xiaozhi.modules.sys.service.SysParamsService;

/**
 * MAC地址认证控制器
 *
 * <AUTHOR>
 * @version 1.0, 2025/6/15
 * @since 1.0.0
 */
@RestController
@RequestMapping("device/auth")
@Tag(name = "MAC地址认证")
@AllArgsConstructor
public class MacAuthController {

    private final MacAddressService macAddressService;
    private final MacBlacklistDao macBlacklistDao;
    private final RedisUtils redisUtils;
    private final SysParamsService sysParamsService;

    // 缓存过期时间（秒）
    private static final int CACHE_TTL = 300; // 5分钟

    @GetMapping("/check/{macAddress}")
    @Operation(summary = "检查MAC地址是否有效")
    public Result<Boolean> checkMacAddress(@PathVariable("macAddress") String macAddress) {
        if (StringUtils.isBlank(macAddress)) {
            return new Result<Boolean>().ok(false);
        }

        // 检查MAC地址格式
        if (!macAddress.matches("^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$")) {
            return new Result<Boolean>().ok(false);
        }

        // 尝试从Redis缓存获取结果
        String cacheKey = RedisKeys.getMacAuthKey(macAddress);
        Object cachedResult = redisUtils.get(cacheKey);

        if (cachedResult != null) {
            if ("blacklisted".equals(cachedResult)) {
                return new Result<Boolean>().error("MAC地址已被禁用");
            } else {
                return new Result<Boolean>().ok(Boolean.valueOf(cachedResult.toString()));
            }
        }

        // 缓存未命中，查询数据库

        // 检查是否在黑名单中
        if (macBlacklistDao.checkMacAddressInBlacklist(macAddress) > 0) {
            // 缓存黑名单结果
            redisUtils.set(cacheKey, "blacklisted", CACHE_TTL);
            return new Result<Boolean>().error("MAC地址已被禁用");
        }

        // 检查是否在白名单中且已启用
        boolean isEnabled = macAddressService.checkMacAddressEnabled(macAddress);

        // 缓存结果
        redisUtils.set(cacheKey, isEnabled, CACHE_TTL);

        return new Result<Boolean>().ok(isEnabled);
    }

    @PostMapping("/authenticate")
    @Operation(summary = "统一MAC地址认证（包含格式验证、频率限制、自动注册）")
    public Result<MacAuthResult> authenticateMacAddress(@RequestBody MacAuthRequest request) {
        String macAddress = request.getMacAddress();
        String clientId = request.getClientId();

        // 1. 基础验证
        if (StringUtils.isBlank(macAddress)) {
            return new Result<MacAuthResult>().error("MAC地址不能为空");
        }

        // 2. MAC地址格式验证
        if (!macAddress.matches("^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$")) {
            return new Result<MacAuthResult>().error("MAC地址格式无效");
        }

        // 3. 访问频率限制检查
        if (!checkAccessRate(macAddress)) {
            return new Result<MacAuthResult>().error("访问频率过高，请稍后再试");
        }

        // 4. 检查是否在黑名单中
        if (macBlacklistDao.checkMacAddressInBlacklist(macAddress) > 0) {
            return new Result<MacAuthResult>().error("MAC地址已被禁用");
        }

        // 5. 检查是否在白名单中
        boolean isInWhitelist = macAddressService.checkMacAddressEnabled(macAddress);

        if (isInWhitelist) {
            // 白名单设备，直接认证成功
            MacAuthResult result = new MacAuthResult();
            result.setAuthenticated(true);
            result.setMethod("mac_whitelist");
            result.setMacAddress(macAddress);
            return new Result<MacAuthResult>().ok(result);
        }

        // 6. 检查是否启用自动注册
        String autoRegisterParam = sysParamsService.getValue("mac_auto_register_enabled", true);
        boolean autoRegisterEnabled = "true".equalsIgnoreCase(autoRegisterParam);

        if (autoRegisterEnabled) {
            // 尝试自动注册
            boolean registered = macAddressService.autoRegisterMacAddress(macAddress, "自动注册设备");
            if (registered) {
                MacAuthResult result = new MacAuthResult();
                result.setAuthenticated(true);
                result.setMethod("auto_register");
                result.setMacAddress(macAddress);
                return new Result<MacAuthResult>().ok(result);
            } else {
                return new Result<MacAuthResult>().error("自动注册失败");
            }
        }

        // 7. 认证失败
        return new Result<MacAuthResult>().error("MAC地址未授权");
    }

    /**
     * 检查访问频率限制
     */
    private boolean checkAccessRate(String macAddress) {
        String rateLimitKey = "mac:rate_limit:" + macAddress;
        String maxAccessParam = sysParamsService.getValue("mac_access_limit", true);
        int maxAccess = StringUtils.isNotBlank(maxAccessParam) ? Integer.parseInt(maxAccessParam) : 10;

        // 获取当前访问次数
        Object currentCount = redisUtils.get(rateLimitKey);
        int count = currentCount != null ? Integer.parseInt(currentCount.toString()) : 0;

        if (count >= maxAccess) {
            return false;
        }

        // 增加访问次数，设置1分钟过期
        redisUtils.set(rateLimitKey, count + 1, 60);
        return true;
    }

    /**
     * MAC认证请求对象
     */
    public static class MacAuthRequest {
        private String macAddress;
        private String clientId;

        // getters and setters
        public String getMacAddress() { return macAddress; }
        public void setMacAddress(String macAddress) { this.macAddress = macAddress; }
        public String getClientId() { return clientId; }
        public void setClientId(String clientId) { this.clientId = clientId; }
    }

    /**
     * MAC认证结果对象
     */
    public static class MacAuthResult {
        private boolean authenticated;
        private String method; // "mac_whitelist" 或 "auto_register"
        private String macAddress;

        // getters and setters
        public boolean isAuthenticated() { return authenticated; }
        public void setAuthenticated(boolean authenticated) { this.authenticated = authenticated; }
        public String getMethod() { return method; }
        public void setMethod(String method) { this.method = method; }
        public String getMacAddress() { return macAddress; }
        public void setMacAddress(String macAddress) { this.macAddress = macAddress; }
    }

    @GetMapping("/invalidate/{macAddress}")
    @Operation(summary = "使MAC地址缓存失效")
    public Result<Void> invalidateCache(@PathVariable("macAddress") String macAddress) {
        if (StringUtils.isBlank(macAddress)) {
            return new Result<Void>().error("MAC地址不能为空");
        }

        String cacheKey = RedisKeys.getMacAuthKey(macAddress);
        redisUtils.delete(cacheKey);

        return new Result<Void>().ok(null);
    }
}
