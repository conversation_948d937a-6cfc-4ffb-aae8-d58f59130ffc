package xiaozhi.modules.agent.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 默认智能体实体
 * 用于管理官方默认智能体配置
 *
 * <AUTHOR>
 * @version 1.0, 2025/5/28
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ai_default_agent")
@Schema(description = "默认智能体信息")
public class DefaultAgentEntity {

    @TableId(type = IdType.ASSIGN_UUID)
    @Schema(description = "默认智能体唯一标识")
    private String id;

    /**
     * 智能体编码 - 固定为 "OFFICIAL"
     */
    @Schema(description = "智能体编码")
    private String agentCode;

    /**
     * 智能体名称
     */
    @Schema(description = "智能体名称")
    private String name;

    /**
     * 智能体描述
     */
    @Schema(description = "智能体描述")
    private String description;

    /**
     * 语音识别模型标识
     */
    @Schema(description = "语音识别模型标识")
    private String asrModelId;

    /**
     * 语音活动检测标识
     */
    @Schema(description = "语音活动检测标识")
    private String vadModelId;

    /**
     * 大语言模型标识
     */
    @Schema(description = "大语言模型标识")
    private String llmModelId;

    /**
     * 语音合成模型标识
     */
    @Schema(description = "语音合成模型标识")
    private String ttsModelId;

    /**
     * 音色标识
     */
    @Schema(description = "音色标识")
    private String ttsVoiceId;

    /**
     * 记忆模型标识
     */
    @Schema(description = "记忆模型标识")
    private String memModelId;

    /**
     * 意图模型标识
     */
    @Schema(description = "意图模型标识")
    private String intentModelId;

    /**
     * 角色设定参数
     */
    @Schema(description = "角色设定参数")
    private String systemPrompt;

    /**
     * 语言编码
     */
    @Schema(description = "语言编码")
    private String langCode;

    /**
     * 交互语种
     */
    @Schema(description = "交互语种")
    private String language;

    /**
     * 是否启用 (1-启用, 0-禁用)
     */
    @Schema(description = "是否启用")
    private Integer enabled;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sort;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;

    /**
     * 更新者
     */
    @Schema(description = "更新者")
    @TableField(fill = FieldFill.UPDATE)
    private Long updater;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateDate;
}