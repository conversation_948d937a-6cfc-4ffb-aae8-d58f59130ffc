-- 为MAC地址白名单表添加注册来源字段
-- changeSet ID: 202501151200_add_register_source_fields
-- 作者: simonchen  
-- 目的: 为MAC地址白名单表添加注册来源和注册时间字段，用于区分后台导入、自动注册、批量导入等不同来源

-- 检查字段是否已存在，避免重复添加
SET @columnExists = (
    SELECT COUNT(1)
    FROM information_schema.columns
    WHERE table_schema = DATABASE()
    AND table_name = 'ai_mac_whitelist'
    AND column_name = 'register_source'
);

-- 添加注册来源字段
SET @sqlStatement = IF(@columnExists = 0,
    'ALTER TABLE `ai_mac_whitelist` ADD COLUMN `register_source` VARCHAR(20) DEFAULT ''manual'' COMMENT ''注册来源(manual:手动导入, auto:自动注册, batch:批量导入)'' AFTER `device_id`',
    'SELECT "字段 register_source 已存在，跳过添加" AS message'
);

PREPARE stmt FROM @sqlStatement;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查注册时间字段是否已存在
SET @columnExists2 = (
    SELECT COUNT(1)
    FROM information_schema.columns
    WHERE table_schema = DATABASE()
    AND table_name = 'ai_mac_whitelist'
    AND column_name = 'register_time'
);

-- 添加注册时间字段
SET @sqlStatement2 = IF(@columnExists2 = 0,
    'ALTER TABLE `ai_mac_whitelist` ADD COLUMN `register_time` DATETIME COMMENT ''注册时间'' AFTER `register_source`',
    'SELECT "字段 register_time 已存在，跳过添加" AS message'
);

PREPARE stmt2 FROM @sqlStatement2;
EXECUTE stmt2;
DEALLOCATE PREPARE stmt2;

-- 检查索引是否已存在
SET @indexExists = (
    SELECT COUNT(1)
    FROM information_schema.statistics
    WHERE table_schema = DATABASE()
    AND table_name = 'ai_mac_whitelist'
    AND index_name = 'idx_ai_mac_whitelist_register_source'
);

-- 添加注册来源索引
SET @sqlStatement3 = IF(@indexExists = 0,
    'ALTER TABLE `ai_mac_whitelist` ADD INDEX `idx_ai_mac_whitelist_register_source` (`register_source`) COMMENT ''注册来源索引''',
    'SELECT "索引 idx_ai_mac_whitelist_register_source 已存在，跳过创建" AS message'
);

PREPARE stmt3 FROM @sqlStatement3;
EXECUTE stmt3;
DEALLOCATE PREPARE stmt3;

-- 为现有数据设置默认值（只更新register_source为NULL的记录）
UPDATE `ai_mac_whitelist` 
SET `register_source` = 'manual', `register_time` = `create_date` 
WHERE `register_source` IS NULL;

-- 验证结果
SELECT 
    COLUMN_NAME as '字段名',
    COLUMN_TYPE as '字段类型',
    COLUMN_DEFAULT as '默认值',
    COLUMN_COMMENT as '注释'
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'ai_mac_whitelist'
AND COLUMN_NAME IN ('register_source', 'register_time')
ORDER BY ORDINAL_POSITION;
