-- Token限制架构修改 - 数据库迁移脚本
-- 版本: 202505281800
-- 描述: 从次数限制改为Token限制的完整数据库结构变更

-- =============================================================================
-- 步骤1: 添加新Token字段到ai_quota_usage表
-- =============================================================================

-- 添加Token相关字段
ALTER TABLE ai_quota_usage 
ADD COLUMN input_tokens INT DEFAULT 0 COMMENT '输入Token数量',
ADD COLUMN output_tokens INT DEFAULT 0 COMMENT '输出Token数量',
ADD COLUMN total_tokens INT DEFAULT 0 COMMENT 'Token总量',
ADD COLUMN model_name VARCHAR(100) COMMENT '模型名称';

-- =============================================================================
-- 步骤2: 更新ai_agent表支持默认智能体
-- =============================================================================

-- 添加默认智能体标识字段
ALTER TABLE ai_agent 
ADD COLUMN is_default TINYINT(1) DEFAULT 0 COMMENT '是否为默认智能体(1-是, 0-否)';

-- =============================================================================
-- 步骤3: 创建默认智能体配置表
-- =============================================================================

CREATE TABLE IF NOT EXISTS ai_default_agent (
    id VARCHAR(50) PRIMARY KEY COMMENT '默认智能体唯一标识',
    agent_code VARCHAR(50) NOT NULL COMMENT '智能体编码',
    name VARCHAR(100) NOT NULL COMMENT '智能体名称',
    description TEXT COMMENT '智能体描述',
    asr_model_id VARCHAR(50) COMMENT '语音识别模型标识',
    vad_model_id VARCHAR(50) COMMENT '语音活动检测标识', 
    llm_model_id VARCHAR(50) COMMENT '大语言模型标识',
    tts_model_id VARCHAR(50) COMMENT '语音合成模型标识',
    tts_voice_id VARCHAR(50) COMMENT '音色标识',
    mem_model_id VARCHAR(50) COMMENT '记忆模型标识',
    intent_model_id VARCHAR(50) COMMENT '意图模型标识',
    system_prompt TEXT COMMENT '角色设定参数',
    lang_code VARCHAR(10) COMMENT '语言编码',
    language VARCHAR(50) COMMENT '交互语种',
    enabled TINYINT(1) DEFAULT 1 COMMENT '是否启用(1-启用, 0-禁用)',
    sort INT DEFAULT 0 COMMENT '排序',
    creator BIGINT COMMENT '创建者',
    create_date DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updater BIGINT COMMENT '更新者',
    update_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='默认智能体配置表';

-- =============================================================================
-- 步骤4: 创建索引优化查询性能
-- =============================================================================

-- 配额使用记录表索引
CREATE INDEX idx_quota_usage_agent_date ON ai_quota_usage(agent_id, usage_date);
CREATE INDEX idx_quota_usage_device_date ON ai_quota_usage(device_mac, usage_date);
CREATE INDEX idx_quota_usage_user_date ON ai_quota_usage(user_id, usage_date);
CREATE INDEX idx_quota_usage_model ON ai_quota_usage(model_name);

-- 智能体表索引
CREATE INDEX idx_agent_is_default ON ai_agent(is_default);
CREATE INDEX idx_agent_user_id ON ai_agent(user_id);

-- 设备表索引 
CREATE INDEX idx_device_agent_id ON ai_device(agent_id);

-- =============================================================================
-- 步骤5: 插入官方默认智能体
-- =============================================================================

INSERT INTO ai_agent (
    id, 
    agent_code, 
    agent_name, 
    user_id, 
    is_default,
    asr_model_id,
    vad_model_id, 
    llm_model_id,
    tts_model_id,
    system_prompt,
    lang_code,
    language,
    sort,
    creator,
    created_at
) VALUES (
    'OFFICIAL_DEFAULT_AGENT', 
    'OFFICIAL', 
    '官方默认智能体', 
    NULL, 
    1,
    'sense_voice_small',
    'silero_vad',
    'deepseek_chat',
    'edge_tts',
    '你是小智，一个友好、智能的AI助手。请用简洁明了的方式回答用户的问题。',
    'zh',
    '中文',
    0,
    1,
    NOW()
) ON DUPLICATE KEY UPDATE 
    agent_name = '官方默认智能体',
    is_default = 1;

-- =============================================================================
-- 步骤6: 更新设备默认绑定到官方智能体
-- =============================================================================

-- 将现有设备绑定到官方默认智能体
UPDATE ai_device 
SET agent_id = 'OFFICIAL_DEFAULT_AGENT' 
WHERE agent_id IS NULL OR agent_id = '';

-- =============================================================================
-- 步骤7: 数据迁移 - 将历史usageValue转换为Token字段
-- =============================================================================

-- 将历史usageValue转换为totalTokens
UPDATE ai_quota_usage 
SET 
    total_tokens = COALESCE(usage_value, 0),
    input_tokens = CASE 
        WHEN usage_type = 'input' THEN COALESCE(usage_value, 0)
        ELSE 0 
    END,
    output_tokens = CASE 
        WHEN usage_type = 'output' THEN COALESCE(usage_value, 0)
        ELSE 0 
    END
WHERE total_tokens IS NULL OR total_tokens = 0;

-- =============================================================================
-- 步骤8: 创建账号Token使用量累计视图
-- =============================================================================

CREATE OR REPLACE VIEW v_account_token_usage AS
SELECT 
    a.user_id,
    qu.usage_date,
    SUM(qu.input_tokens) as total_input_tokens,
    SUM(qu.output_tokens) as total_output_tokens, 
    SUM(qu.total_tokens) as total_tokens
FROM ai_quota_usage qu
INNER JOIN ai_agent a ON qu.agent_id = a.id
WHERE a.user_id IS NOT NULL  -- 排除默认智能体
GROUP BY a.user_id, qu.usage_date;

-- =============================================================================
-- 步骤9: 创建设备Token使用量视图
-- =============================================================================

CREATE OR REPLACE VIEW v_device_token_usage AS
SELECT 
    qu.device_mac,
    qu.usage_date,
    SUM(qu.input_tokens) as total_input_tokens,
    SUM(qu.output_tokens) as total_output_tokens,
    SUM(qu.total_tokens) as total_tokens,
    qu.agent_id
FROM ai_quota_usage qu
INNER JOIN ai_agent a ON qu.agent_id = a.id
WHERE a.is_default = 1  -- 仅统计默认智能体的使用量
GROUP BY qu.device_mac, qu.usage_date, qu.agent_id;

-- =============================================================================
-- 完成标记
-- =============================================================================

-- 插入迁移完成标记
INSERT INTO sys_params (param_code, param_value, remark, creator, create_date)
SELECT 'TOKEN_MIGRATION_COMPLETED', '2025-05-28', 'Token限制架构迁移完成标记', 1, NOW()
WHERE NOT EXISTS (
    SELECT 1 FROM sys_params WHERE param_code = 'TOKEN_MIGRATION_COMPLETED'
);

-- 更新已存在的记录
UPDATE sys_params
SET param_value = '2025-05-28', update_date = NOW()
WHERE param_code = 'TOKEN_MIGRATION_COMPLETED';