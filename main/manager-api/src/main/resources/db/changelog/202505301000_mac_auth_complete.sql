-- MAC地址认证系统完整数据库变更
-- changeSet ID: ************_mac_auth_complete
-- 作者: simonchen
-- 目的: MAC地址认证系统完整实现（合并优化版本）
-- 合并原变更集: ************, ************, ************, ************

-- ==================== 表结构创建 ====================

-- 1. 配额设置表
DROP TABLE IF EXISTS `ai_quota_settings`;
CREATE TABLE `ai_quota_settings` (
    `id` BIGINT AUTO_INCREMENT COMMENT '主键ID' PRIMARY KEY,
    `quota_type` VARCHAR(20) NOT NULL COMMENT '配额类型(device/account)',
    `quota_value` INT NOT NULL DEFAULT 0 COMMENT '配额值(0表示不限制)',
    `reset_type` VARCHAR(20) NOT NULL DEFAULT 'daily' COMMENT '重置类型(daily/monthly/never)',
    `description` VARCHAR(255) COMMENT '配额描述',
    `creator` BIGINT COMMENT '创建者',
    `create_date` DATETIME COMMENT '创建时间',
    `updater` BIGINT COMMENT '更新者',
    `update_date` DATETIME COMMENT '更新时间',
    UNIQUE INDEX `idx_ai_quota_settings_type` (`quota_type`) COMMENT '配额类型唯一索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配额设置表';

-- 2. 配额使用记录表
DROP TABLE IF EXISTS `ai_quota_usage`;
CREATE TABLE `ai_quota_usage` (
    `id` BIGINT AUTO_INCREMENT COMMENT '主键ID' PRIMARY KEY,
    `user_id` BIGINT COMMENT '用户ID',
    `device_mac` VARCHAR(50) COMMENT '设备MAC地址',
    `agent_id` VARCHAR(32) COMMENT '智能体ID',
    `usage_type` VARCHAR(20) NOT NULL COMMENT '使用类型(input/output)',
    `usage_value` INT NOT NULL DEFAULT 0 COMMENT '使用量',
    `usage_date` DATE NOT NULL COMMENT '使用日期',
    `create_date` DATETIME COMMENT '创建时间',
    INDEX `idx_ai_quota_usage_user_date` (`user_id`, `usage_date`) COMMENT '用户日期索引',
    INDEX `idx_ai_quota_usage_device_date` (`device_mac`, `usage_date`) COMMENT '设备日期索引',
    INDEX `idx_ai_quota_usage_agent_date` (`agent_id`, `usage_date`) COMMENT '智能体日期索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配额使用记录表';

-- 3. MAC地址黑名单表
DROP TABLE IF EXISTS `ai_mac_blacklist`;
CREATE TABLE `ai_mac_blacklist` (
    `id` BIGINT AUTO_INCREMENT COMMENT '主键ID' PRIMARY KEY,
    `mac_address` VARCHAR(50) NOT NULL COMMENT 'MAC地址',
    `reason` VARCHAR(255) COMMENT '禁用原因',
    `expire_date` DATETIME COMMENT '过期时间(NULL表示永久)',
    `creator` BIGINT COMMENT '创建者',
    `create_date` DATETIME COMMENT '创建时间',
    `updater` BIGINT COMMENT '更新者',
    `update_date` DATETIME COMMENT '更新时间',
    UNIQUE INDEX `idx_ai_mac_blacklist_mac` (`mac_address`) COMMENT 'MAC地址唯一索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='MAC地址黑名单表';

-- 4. MAC地址白名单表
DROP TABLE IF EXISTS `ai_mac_whitelist`;
CREATE TABLE `ai_mac_whitelist` (
    `id` BIGINT AUTO_INCREMENT COMMENT '主键ID' PRIMARY KEY,
    `mac_address` VARCHAR(50) NOT NULL COMMENT 'MAC地址',
    `remark` VARCHAR(255) COMMENT '设备备注',
    `status` TINYINT UNSIGNED DEFAULT 1 COMMENT '状态(0禁用/1启用)',
    `bound` TINYINT UNSIGNED DEFAULT 0 COMMENT '是否已绑定设备(0未绑定/1已绑定)',
    `device_id` VARCHAR(32) COMMENT '关联设备ID',
    `creator` BIGINT COMMENT '创建者',
    `create_date` DATETIME COMMENT '创建时间',
    `updater` BIGINT COMMENT '更新者',
    `update_date` DATETIME COMMENT '更新时间',
    UNIQUE INDEX `idx_ai_mac_whitelist_mac` (`mac_address`) COMMENT 'MAC地址唯一索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='MAC地址白名单表';

-- 5. 设备指纹表
DROP TABLE IF EXISTS `ai_device_fingerprint`;
CREATE TABLE `ai_device_fingerprint` (
    `id` BIGINT AUTO_INCREMENT COMMENT '主键ID' PRIMARY KEY,
    `mac_address` VARCHAR(50) NOT NULL COMMENT 'MAC地址',
    `device_model` VARCHAR(100) COMMENT '设备型号',
    `firmware_version` VARCHAR(50) COMMENT '固件版本',
    `chip_id` VARCHAR(100) COMMENT '芯片ID',
    `last_ip` VARCHAR(50) COMMENT '最后连接IP',
    `status` TINYINT UNSIGNED DEFAULT 1 COMMENT '状态(0禁用/1启用)',
    `creator` BIGINT COMMENT '创建者',
    `create_date` DATETIME COMMENT '创建时间',
    `updater` BIGINT COMMENT '更新者',
    `update_date` DATETIME COMMENT '更新时间',
    UNIQUE INDEX `idx_ai_device_fingerprint_mac` (`mac_address`) COMMENT 'MAC地址唯一索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备指纹表';

-- ==================== 索引优化 ====================

-- 添加设备表MAC地址索引（如果不存在）
SET @indexExists = (
    SELECT COUNT(1)
    FROM information_schema.statistics
    WHERE table_schema = DATABASE()
    AND table_name = 'ai_device'
    AND index_name = 'idx_ai_device_mac_address'
);

SET @sqlStatement = IF(@indexExists = 0,
    'ALTER TABLE `ai_device` ADD INDEX `idx_ai_device_mac_address` (`mac_address`) COMMENT "MAC地址索引"',
    'SELECT "索引 idx_ai_device_mac_address 已存在，跳过创建" AS message'
);

PREPARE stmt FROM @sqlStatement;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ==================== 初始化数据 ====================

-- 清理并重建配额设置
DELETE FROM `ai_quota_settings` WHERE `quota_type` IN ('device', 'account');

-- 初始化配额设置
INSERT INTO `ai_quota_settings` (`quota_type`, `quota_value`, `reset_type`, `description`, `create_date`)
VALUES
('device', 10000, 'daily', '设备每日配额', NOW()),
('account', 100000, 'daily', '账号每日配额', NOW());

-- ==================== 系统参数配置 ====================

-- 彻底清理所有可能存在冲突的参数
DELETE FROM `sys_params` WHERE `param_code` IN (
    -- 旧的MAC认证参数
    'mac_access_limit', 'enable_mac_auth', 'enable_quota_limit',
    'mac_auth_enabled', 'mac_auto_register', 'device_fingerprint_enabled',
    'mac_auto_register_enabled',
    -- 新的MAC认证参数
    'mac_auth.enabled', 'mac_auth.access_limit', 'mac_auth.cache_ttl',
    'mac_auth.auto_register', 'mac_auth.enable_device_fingerprint',
    -- 配额相关参数
    'quota.enabled', 'quota.device_default_quota', 'quota.account_default_quota'
);

-- 清理可能存在冲突的ID范围
DELETE FROM `sys_params` WHERE `id` BETWEEN 1001 AND 1008;

-- MAC认证核心参数（统一使用mac_auth.*命名规范）
INSERT INTO `sys_params` (`id`, `param_code`, `param_value`, `value_type`, `param_type`, `remark`, `create_date`) VALUES
(1001, 'mac_auth.enabled', 'true', 'boolean', 1, 'MAC地址认证启用状态', NOW()),
(1002, 'mac_auth.access_limit', '10', 'number', 1, 'MAC地址访问频率限制（次/分钟）', NOW()),
(1003, 'mac_auth.cache_ttl', '300', 'number', 1, 'MAC地址缓存有效期（秒）', NOW()),
(1004, 'mac_auth.auto_register', 'false', 'boolean', 1, '是否允许MAC地址自动注册', NOW()),
(1005, 'mac_auth.enable_device_fingerprint', 'true', 'boolean', 1, 'MAC地址设备指纹验证启用状态', NOW());

-- 配额管理参数
INSERT INTO `sys_params` (`id`, `param_code`, `param_value`, `value_type`, `param_type`, `remark`, `create_date`) VALUES
(1006, 'quota.enabled', 'true', 'boolean', 1, '是否启用配额限制', NOW()),
(1007, 'quota.device_default_quota', '10000', 'number', 1, '设备默认每日配额', NOW()),
(1008, 'quota.account_default_quota', '100000', 'number', 1, '账号默认每日配额', NOW());

-- ==================== 验证结果 ====================

-- 验证表创建结果
SELECT
    TABLE_NAME as '表名',
    TABLE_COMMENT as '表注释',
    TABLE_ROWS as '行数'
FROM information_schema.TABLES
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME IN (
    'ai_quota_settings', 'ai_quota_usage', 'ai_mac_blacklist',
    'ai_mac_whitelist', 'ai_device_fingerprint'
)
ORDER BY TABLE_NAME;

-- 验证系统参数配置
SELECT
    param_code as '参数代码',
    param_value as '参数值',
    remark as '说明'
FROM sys_params
WHERE param_code LIKE 'mac_auth%' OR param_code LIKE 'quota%'
ORDER BY param_code;