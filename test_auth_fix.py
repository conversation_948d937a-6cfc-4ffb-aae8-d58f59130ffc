#!/usr/bin/env python3
"""
测试MAC地址认证修复效果
验证当MAC地址不在白名单中时，连接是否被正确拒绝，以及错误提示信息是否正确
"""

import asyncio
import websockets
import json
from websockets.exceptions import ConnectionClosedError, InvalidStatusCode

# 测试配置
WS_URL = "ws://127.0.0.1:8100"
MAC_ADDRESS = "C9:49:85:69:83:33"  # 已从数据库删除的MAC地址


async def test_auth_failure():
    """测试认证失败的情况"""
    print(f"测试MAC地址认证失败: {MAC_ADDRESS}")
    print("=" * 50)

    try:
        # 连接WebSocket
        headers = {
            "device-id": MAC_ADDRESS,
            "client-id": "test_client"
        }

        print(f"正在连接 {WS_URL}...")
        print(f"Headers: {headers}")

        async with websockets.connect(WS_URL, extra_headers=headers) as websocket:
            print("✅ WebSocket连接成功")

            # 等待服务器响应
            try:
                message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                print(f"收到服务器消息: {message}")

                # 解析消息
                try:
                    data = json.loads(message)
                    if data.get("type") == "error" and data.get("code") == 401:
                        print("✅ 认证失败，服务器正确返回错误消息")
                        error_msg = data.get('message', '')
                        print(f"   错误消息: {error_msg}")
                        print(f"   详细信息: {data.get('details')}")

                        # 检查错误提示信息类型
                        if "官网激活" in error_msg or "设备未激活" in error_msg:
                            print("✅ 错误提示正确：MAC认证模式，提示用户前往官网激活设备")
                        elif "控制面板" in error_msg and "绑定设备" in error_msg:
                            print("⚠️  错误提示为Token认证模式：提示用户绑定设备")
                        else:
                            print(f"❓ 未知错误提示类型: {error_msg}")
                    else:
                        print("❌ 服务器返回了非预期的消息")
                        print(f"   消息类型: {data.get('type')}")
                        print(f"   消息内容: {data}")
                except json.JSONDecodeError:
                    print(f"❌ 服务器返回了非JSON消息: {message}")

                # 尝试发送hello消息，看是否还能通信
                hello_message = {
                    "type": "hello",
                    "device_id": MAC_ADDRESS,
                    "device_name": "测试设备",
                    "device_mac": MAC_ADDRESS,
                    "token": "test-token"
                }

                print("\n尝试发送hello消息...")
                await websocket.send(json.dumps(hello_message))

                # 等待响应
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                    print(f"❌ 意外收到响应: {response}")
                    print("   这表明连接没有被正确关闭")
                except asyncio.TimeoutError:
                    print("✅ 没有收到响应，连接可能已被关闭")
                except websockets.exceptions.ConnectionClosed:
                    print("✅ 连接已被服务器关闭")

            except asyncio.TimeoutError:
                print("❌ 等待服务器响应超时")
            except websockets.exceptions.ConnectionClosed:
                print("✅ 连接被服务器关闭（这是预期的）")

    except websockets.exceptions.ConnectionClosed as e:
        print(f"✅ 连接被关闭: {e}")
    except Exception as e:
        print(f"❌ 连接失败: {e}")


async def test_valid_mac():
    """测试有效MAC地址（如果有的话）"""
    print(f"\n测试有效MAC地址认证")
    print("=" * 50)

    # 这里可以测试一个有效的MAC地址
    valid_mac = "aa:bb:cc:dd:ee:ff"  # 假设这个在白名单中

    try:
        headers = {
            "device-id": valid_mac,
            "client-id": "test_client"
        }

        print(f"正在连接 {WS_URL}...")
        print(f"Headers: {headers}")

        async with websockets.connect(WS_URL, extra_headers=headers) as websocket:
            print("✅ WebSocket连接成功")

            # 等待服务器响应
            try:
                message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                print(f"收到服务器消息: {message}")

                # 解析消息
                try:
                    data = json.loads(message)
                    if data.get("type") == "error":
                        print("❌ 有效MAC地址也被拒绝了")
                        print(f"   错误消息: {data.get('message')}")
                    else:
                        print("✅ 有效MAC地址认证通过")
                        print(f"   消息类型: {data.get('type')}")
                except json.JSONDecodeError:
                    print(f"收到非JSON消息: {message}")

            except asyncio.TimeoutError:
                print("❌ 等待服务器响应超时")
            except websockets.exceptions.ConnectionClosed:
                print("❌ 连接被意外关闭")

    except Exception as e:
        print(f"连接失败: {e}")


async def main():
    """主测试函数"""
    print("WebSocket认证修复测试")
    print("=" * 60)

    # 测试认证失败的情况
    await test_auth_failure()

    # 等待一下
    await asyncio.sleep(2)

    # 测试有效MAC地址（可选）
    # await test_valid_mac()

    print("\n" + "=" * 60)
    print("测试完成！")
    print("\n预期结果：")
    print("1. MAC认证模式：提示'设备未激活，请前往官网激活您的设备'")
    print("2. Token认证模式：提示'请登录控制面板，输入绑定码，绑定设备'")
    print("3. 连接应该被服务器主动关闭")
    print("4. 不应该能够继续发送消息")


if __name__ == "__main__":
    asyncio.run(main())
