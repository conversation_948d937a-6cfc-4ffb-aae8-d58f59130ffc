#!/usr/bin/env python3
"""
测试配置加载脚本
用于验证MAC认证配置是否正确从API获取
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'main/xiaozhi-server'))

from config.config_loader import load_config
import json

def test_config():
    """测试配置加载"""
    try:
        # 加载配置
        config = load_config()
        
        print("=== 配置加载测试 ===")
        print(f"配置来源: {'API' if config.get('read_config_from_api') else '本地文件'}")
        
        # 检查MAC认证配置
        mac_auth_config = config.get("mac_auth", {})
        print(f"\nMAC认证配置:")
        print(f"  enabled: {mac_auth_config.get('enabled')}")
        print(f"  auto_register: {mac_auth_config.get('auto_register')}")
        print(f"  access_limit: {mac_auth_config.get('access_limit')}")
        print(f"  cache_ttl: {mac_auth_config.get('cache_ttl')}")
        print(f"  enable_device_fingerprint: {mac_auth_config.get('enable_device_fingerprint')}")
        
        # 检查manager-api配置
        manager_api_config = config.get("manager-api", {})
        print(f"\nManager API配置:")
        print(f"  url: {manager_api_config.get('url')}")
        print(f"  secret: {manager_api_config.get('secret', '***')[:10]}...")
        
        # 如果配置来源是API，显示更多信息
        if config.get('read_config_from_api'):
            print(f"\n从API获取的配置键:")
            for key in sorted(config.keys()):
                if key not in ['manager-api', 'server']:
                    print(f"  {key}")
        
        return True
        
    except Exception as e:
        print(f"配置加载失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_config()
    sys.exit(0 if success else 1)
