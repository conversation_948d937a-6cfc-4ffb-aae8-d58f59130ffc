#!/usr/bin/env python3
"""
检查MAC地址缓存和数据库状态的脚本
用于排查MAC地址认证问题
"""

import asyncio
import aiohttp
import json
import sys
import os

# 配置信息
API_BASE_URL = "http://************:8002/xiaozhi"
API_SECRET = "56086f15-a833-4d81-83e7-240e85858541"
MAC_ADDRESS = "C9:49:85:69:83:33"


async def check_mac_auth_api():
    """检查MAC地址认证API"""
    print("=== 检查MAC地址认证API ===")
    
    url = f"{API_BASE_URL}/device/auth/check/{MAC_ADDRESS}"
    headers = {
        "Authorization": f"Bearer {API_SECRET}",
        "Content-Type": "application/json"
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=headers, timeout=aiohttp.ClientTimeout(total=10)) as response:
                print(f"HTTP状态码: {response.status}")
                
                if response.status == 200:
                    data = await response.json()
                    print(f"API响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
                    
                    if data.get("code") == 0:
                        is_whitelisted = data.get("data", False)
                        print(f"MAC地址 {MAC_ADDRESS} 在白名单中: {is_whitelisted}")
                        return is_whitelisted
                    else:
                        print(f"API返回错误: {data.get('msg', '未知错误')}")
                        return False
                else:
                    text = await response.text()
                    print(f"API请求失败: {text}")
                    return False
                    
    except Exception as e:
        print(f"API请求异常: {e}")
        return False


async def check_mac_whitelist_api():
    """检查MAC地址白名单API"""
    print("\n=== 检查MAC地址白名单API ===")
    
    url = f"{API_BASE_URL}/device/mac/whitelist"
    headers = {
        "Authorization": f"Bearer {API_SECRET}",
        "Content-Type": "application/json"
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=headers, timeout=aiohttp.ClientTimeout(total=10)) as response:
                print(f"HTTP状态码: {response.status}")
                
                if response.status == 200:
                    data = await response.json()
                    print(f"API响应码: {data.get('code')}")
                    
                    if data.get("code") == 0:
                        whitelist = data.get("data", [])
                        print(f"白名单总数: {len(whitelist)}")
                        
                        if MAC_ADDRESS in whitelist:
                            print(f"✅ MAC地址 {MAC_ADDRESS} 在白名单中")
                            return True
                        else:
                            print(f"❌ MAC地址 {MAC_ADDRESS} 不在白名单中")
                            return False
                    else:
                        print(f"API返回错误: {data.get('msg', '未知错误')}")
                        return False
                else:
                    text = await response.text()
                    print(f"API请求失败: {text}")
                    return False
                    
    except Exception as e:
        print(f"API请求异常: {e}")
        return False


async def clear_mac_cache():
    """清理MAC地址缓存"""
    print("\n=== 清理MAC地址缓存 ===")
    
    # 这里需要调用清理缓存的API
    # 根据代码分析，需要清理以下Redis键：
    # 1. mac:auth:{macAddress}
    # 2. mac:whitelist:set
    # 3. mac:whitelist:page:*
    # 4. mac:whitelist:count
    
    cache_keys_to_clear = [
        f"mac:auth:{MAC_ADDRESS}",
        "mac:whitelist:set",
        "mac:whitelist:count"
    ]
    
    print("需要清理的缓存键:")
    for key in cache_keys_to_clear:
        print(f"  - {key}")
    
    print("\n注意：需要在Redis中手动清理这些键，或者重启Java服务来清理缓存")
    
    # 如果有清理缓存的API，可以在这里调用
    # 目前看起来没有专门的清理缓存API，需要手动操作


def print_redis_commands():
    """打印Redis清理命令"""
    print("\n=== Redis清理命令 ===")
    print("请在Redis客户端中执行以下命令来清理缓存：")
    print()
    print(f"# 清理特定MAC地址的认证缓存")
    print(f"DEL mac:auth:{MAC_ADDRESS}")
    print()
    print(f"# 清理MAC地址白名单集合缓存")
    print(f"DEL mac:whitelist:set")
    print()
    print(f"# 清理MAC地址白名单计数缓存")
    print(f"DEL mac:whitelist:count")
    print()
    print(f"# 清理所有MAC地址白名单分页缓存")
    print(f"EVAL \"return redis.call('del', unpack(redis.call('keys', ARGV[1])))\" 0 mac:whitelist:page:*")
    print()
    print(f"# 或者清理所有MAC相关缓存")
    print(f"EVAL \"return redis.call('del', unpack(redis.call('keys', ARGV[1])))\" 0 mac:*")


def print_sql_commands():
    """打印SQL检查命令"""
    print("\n=== SQL检查命令 ===")
    print("请在MySQL客户端中执行以下命令来检查数据库状态：")
    print()
    print(f"# 检查MAC地址在白名单表中的状态")
    print(f"SELECT * FROM ai_mac_whitelist WHERE mac_address = '{MAC_ADDRESS}';")
    print()
    print(f"# 检查MAC地址在黑名单表中的状态")
    print(f"SELECT * FROM ai_mac_blacklist WHERE mac_address = '{MAC_ADDRESS}';")
    print()
    print(f"# 如果要彻底删除MAC地址（谨慎操作）")
    print(f"DELETE FROM ai_mac_whitelist WHERE mac_address = '{MAC_ADDRESS}';")
    print()
    print(f"# 如果要禁用MAC地址（推荐）")
    print(f"UPDATE ai_mac_whitelist SET status = 0 WHERE mac_address = '{MAC_ADDRESS}';")


async def main():
    """主函数"""
    print(f"检查MAC地址: {MAC_ADDRESS}")
    print(f"API地址: {API_BASE_URL}")
    print("=" * 60)
    
    # 1. 检查MAC地址认证API
    auth_result = await check_mac_auth_api()
    
    # 2. 检查MAC地址白名单API
    whitelist_result = await check_mac_whitelist_api()
    
    # 3. 分析结果
    print("\n=== 结果分析 ===")
    if auth_result:
        print("❌ 问题确认：MAC地址认证API返回True，设备仍能通过认证")
        print("可能原因：")
        print("  1. Redis缓存中仍有该MAC地址的认证结果")
        print("  2. 数据库中该MAC地址状态为启用(status=1)")
        print("  3. 该MAC地址仍在白名单表中")
    else:
        print("✅ MAC地址认证API返回False，认证已正确拒绝")
    
    if whitelist_result:
        print("❌ 问题确认：MAC地址仍在白名单中")
    else:
        print("✅ MAC地址不在白名单中")
    
    # 4. 提供解决方案
    print("\n=== 解决方案 ===")
    if auth_result or whitelist_result:
        print("建议按以下步骤操作：")
        print("1. 首先检查数据库状态")
        print("2. 然后清理Redis缓存")
        print("3. 最后重新测试")
        
        print_sql_commands()
        print_redis_commands()
    else:
        print("MAC地址认证已正确配置，无需额外操作")


if __name__ == "__main__":
    asyncio.run(main())
