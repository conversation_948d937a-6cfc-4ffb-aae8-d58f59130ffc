# 第一阶段：构建Python依赖
FROM python:3.10-slim AS builder

WORKDIR /app

COPY main/xiaozhi-server/requirements.txt .

# 配置pip使用国内镜像源并安装Python依赖
RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/ && \
    pip config set global.trusted-host mirrors.aliyun.com && \
    pip config set global.timeout 300 && \
    pip config set global.retries 5 && \
    pip config set global.extra-index-url "https://mirrors.cloud.tencent.com/pypi/simple/ https://pypi.tuna.tsinghua.edu.cn/simple/" && \
    pip install --no-cache-dir --upgrade pip setuptools wheel && \
    pip install --no-cache-dir -r requirements.txt || \
    (echo "阿里云镜像失败，尝试腾讯云镜像..." && \
     pip config set global.index-url https://mirrors.cloud.tencent.com/pypi/simple/ && \
     pip config set global.trusted-host mirrors.cloud.tencent.com && \
     pip install --no-cache-dir -r requirements.txt) || \
    (echo "腾讯云镜像失败，尝试清华大学镜像..." && \
     pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple/ && \
     pip config set global.trusted-host pypi.tuna.tsinghua.edu.cn && \
     pip install --no-cache-dir -r requirements.txt)

# 第二阶段：生产镜像
FROM python:3.10-slim

WORKDIR /opt/xiaozhi-esp32-server

# 配置pip镜像源（生产环境备用）
RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/ && \
    pip config set global.trusted-host mirrors.aliyun.com && \
    pip config set global.timeout 300 && \
    pip config set global.retries 5

# 使用阿里云镜像源替换默认的Debian源并安装系统依赖
RUN sed -i 's|http://deb.debian.org|http://mirrors.aliyun.com|g' /etc/apt/sources.list.d/debian.sources && \
    apt-get update && \
    apt-get install -y --no-install-recommends libopus0 ffmpeg && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# 从构建阶段复制Python包和前端构建产物
COPY --from=builder /usr/local/lib/python3.10/site-packages /usr/local/lib/python3.10/site-packages

# 复制应用代码
COPY main/xiaozhi-server .

# 启动应用
CMD ["python", "app.py"]