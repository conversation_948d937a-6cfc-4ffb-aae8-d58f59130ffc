#!/usr/bin/env python3
"""
测试MAC认证播报修复
"""
import asyncio
import websockets
import json
import time

async def test_mac_auth_broadcast():
    """测试MAC认证模式下的播报信息"""
    print("测试MAC认证播报修复")
    print("=" * 60)
    print("测试MAC地址认证失败播报: FF:FF:FF:FF:FF:FF")
    print("=" * 50)

    uri = "ws://127.0.0.1:8100"
    headers = {
        'device-id': 'FF:FF:FF:FF:FF:FF',  # 使用一个肯定不存在的MAC地址
        'client-id': 'test_client'
    }

    try:
        print(f"正在连接 {uri}...")
        print(f"Headers: {headers}")

        async with websockets.connect(uri, extra_headers=headers) as websocket:
            print("✅ WebSocket连接成功")

            # 等待欢迎消息
            try:
                welcome_msg = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                print(f"📨 收到欢迎消息: {welcome_msg}")

                # 发送一个音频消息来触发绑定检查
                print("🎤 发送测试音频数据...")
                test_audio = b'\x00\x01\x02\x03'  # 简单的测试音频数据
                await websocket.send(test_audio)

                # 等待响应消息
                print("⏳ 等待播报消息...")
                start_time = time.time()

                while time.time() - start_time < 15:  # 等待15秒
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                        print(f"📨 收到消息: {message}")

                        # 解析JSON消息
                        try:
                            msg_data = json.loads(message)
                            if msg_data.get("type") == "tts":
                                content = msg_data.get("content", "")
                                print(f"🔊 TTS播报内容: {content}")

                                # 检查是否包含预期的MAC认证提示
                                if "设备未激活" in content and "官网激活" in content:
                                    print("✅ 成功！播报了正确的MAC认证提示信息")
                                    return True
                                elif "绑定码" in content:
                                    print("❌ 错误！播报了Token认证的绑定码信息")
                                    return False
                                else:
                                    print(f"ℹ️  其他TTS内容: {content}")
                        except json.JSONDecodeError:
                            print(f"📄 非JSON消息: {message}")

                    except asyncio.TimeoutError:
                        print("⏳ 等待消息超时，继续等待...")
                        continue

                print("⚠️  未收到预期的播报消息")
                return False

            except asyncio.TimeoutError:
                print("❌ 等待欢迎消息超时")
                return False

    except websockets.exceptions.ConnectionClosed as e:
        print(f"❌ 连接被关闭: {e}")
        return False
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

async def main():
    success = await test_mac_auth_broadcast()

    print("\n" + "=" * 60)
    print("测试完成！")
    print("\n预期结果：")
    print("1. MAC认证模式：播报'设备未激活，请前往官网激活您的设备'")
    print("2. Token认证模式：播报'请登录控制面板，输入绑定码，绑定设备'")
    print("3. 设备应该能够正常连接并接收播报")

    if success:
        print("\n✅ 测试通过！")
    else:
        print("\n❌ 测试失败！")

if __name__ == "__main__":
    asyncio.run(main())
