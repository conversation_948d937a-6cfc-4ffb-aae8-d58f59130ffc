#!/usr/bin/env python3
"""
测试MAC地址自动注册和认证功能
"""

import asyncio
import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'main/xiaozhi-server'))

from core.hybrid_auth import HybridAuthMiddleware, _is_valid_mac_format


def test_mac_format_validation():
    """测试MAC地址格式验证"""
    print("=== 测试MAC地址格式验证 ===")
    
    valid_macs = [
        "C9:49:85:69:83:33",
        "AA:BB:CC:DD:EE:FF",
        "00:11:22:33:44:55",
        "C9-49-85-69-83-33",
        "AA-BB-CC-DD-EE-FF"
    ]
    
    invalid_macs = [
        "C9:49:85:69:83",  # 缺少一段
        "C9:49:85:69:83:33:44",  # 多一段
        "GG:49:85:69:83:33",  # 无效字符
        "C9:49:85:69:83:3G",  # 无效字符
        "",  # 空字符串
        "not-a-mac"  # 完全无效
    ]
    
    print("有效的MAC地址:")
    for mac in valid_macs:
        result = _is_valid_mac_format(mac)
        print(f"  {mac}: {result}")
        assert result, f"MAC地址 {mac} 应该是有效的"
    
    print("\n无效的MAC地址:")
    for mac in invalid_macs:
        result = _is_valid_mac_format(mac)
        print(f"  {mac}: {result}")
        assert not result, f"MAC地址 {mac} 应该是无效的"
    
    print("✅ MAC地址格式验证测试通过")


async def test_hybrid_auth_initialization():
    """测试混合认证中间件初始化"""
    print("\n=== 测试混合认证中间件初始化 ===")
    
    # 测试配置
    config = {
        "mac_auth": {
            "enabled": True,
            "auto_register": True
        },
        "manager-api": {
            "url": "http://localhost:8002/xiaozhi",
            "secret": "test-secret"
        },
        "server": {
            "auth": {
                "enabled": False
            }
        }
    }
    
    try:
        auth_middleware = HybridAuthMiddleware(config)
        print(f"✅ 混合认证中间件初始化成功")
        print(f"  MAC认证启用: {auth_middleware.mac_auth_enabled}")
        print(f"  自动注册启用: {auth_middleware.auto_register_enabled}")
        
        assert auth_middleware.mac_auth_enabled == True
        assert auth_middleware.auto_register_enabled == True
        
    except Exception as e:
        print(f"❌ 混合认证中间件初始化失败: {e}")
        raise


def test_headers_processing():
    """测试headers处理逻辑"""
    print("\n=== 测试headers处理逻辑 ===")
    
    # 模拟MAC认证成功的headers
    mac_auth_headers = {
        "device-id": "C9:49:85:69:83:33",
        "auth_success": "true",
        "auth_method": "auto_register",
        "device_bound": "true"
    }
    
    # 模拟Token认证的headers
    token_auth_headers = {
        "device-id": "C9:49:85:69:83:33",
        "authorization": "Bearer your-token1"
    }
    
    # 测试MAC认证检查逻辑
    is_mac_authenticated = (
        mac_auth_headers.get("auth_method") in ["mac_whitelist", "auto_register"] and
        mac_auth_headers.get("auth_success") == "true"
    )
    
    print(f"MAC认证headers检查结果: {is_mac_authenticated}")
    assert is_mac_authenticated, "MAC认证headers应该被正确识别"
    
    # 测试Token认证检查逻辑
    is_token_authenticated = (
        token_auth_headers.get("auth_method") in ["mac_whitelist", "auto_register"] and
        token_auth_headers.get("auth_success") == "true"
    )
    
    print(f"Token认证headers检查结果: {is_token_authenticated}")
    assert not is_token_authenticated, "Token认证headers不应该被识别为MAC认证"
    
    print("✅ headers处理逻辑测试通过")


async def main():
    """主测试函数"""
    print("开始测试MAC地址自动注册和认证功能\n")
    
    try:
        # 测试MAC地址格式验证
        test_mac_format_validation()
        
        # 测试混合认证中间件初始化
        await test_hybrid_auth_initialization()
        
        # 测试headers处理逻辑
        test_headers_processing()
        
        print("\n🎉 所有测试通过！MAC地址自动注册功能已正确实现")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
