#!/usr/bin/env python3
"""
修复Redis缓存不一致问题
"""

import redis

# 配置信息
MAC_ADDRESS = "C9:49:85:69:83:33"

# Redis配置
REDIS_CONFIG = {
    'host': '127.0.0.1',
    'port': 6379,
    'db': 0,
    'password': None
}


def fix_redis_cache():
    """修复Redis缓存"""
    print(f"修复MAC地址缓存: {MAC_ADDRESS}")
    print("=" * 50)
    
    try:
        # 连接Redis
        r = redis.Redis(
            host=REDIS_CONFIG['host'],
            port=REDIS_CONFIG['port'],
            db=REDIS_CONFIG['db'],
            decode_responses=True
        )
        
        # 测试连接
        r.ping()
        print("✅ Redis连接成功")
        
        # 检查当前状态
        whitelist_set_key = "mac:whitelist:set"
        print(f"\n=== 修复前状态 ===")
        
        is_in_set = r.sismember(whitelist_set_key, MAC_ADDRESS)
        set_size = r.scard(whitelist_set_key)
        print(f"白名单集合大小: {set_size}")
        print(f"MAC地址在集合中: {is_in_set}")
        
        if set_size > 0:
            all_members = r.smembers(whitelist_set_key)
            print(f"集合所有成员: {list(all_members)}")
        
        # 执行修复
        print(f"\n=== 执行修复 ===")
        
        if is_in_set:
            # 方法1：从集合中移除特定MAC地址
            removed_count = r.srem(whitelist_set_key, MAC_ADDRESS)
            print(f"从白名单集合中移除MAC地址: {removed_count} 个")
        else:
            print("MAC地址不在集合中，无需移除")
        
        # 方法2：清理整个白名单集合缓存（推荐）
        print("\n清理整个白名单集合缓存...")
        deleted = r.delete(whitelist_set_key)
        print(f"删除白名单集合缓存: {deleted} 个键")
        
        # 清理其他相关缓存
        cache_keys_to_clear = [
            f"mac:auth:{MAC_ADDRESS}",
            "mac:whitelist:count",
        ]
        
        print("\n清理其他相关缓存...")
        for key in cache_keys_to_clear:
            deleted = r.delete(key)
            if deleted > 0:
                print(f"删除缓存键 {key}: {deleted} 个")
        
        # 清理分页缓存
        page_keys = r.keys("mac:whitelist:page:*")
        if page_keys:
            deleted = r.delete(*page_keys)
            print(f"删除分页缓存: {deleted} 个键")
        
        # 检查修复后状态
        print(f"\n=== 修复后状态 ===")
        
        is_in_set_after = r.sismember(whitelist_set_key, MAC_ADDRESS)
        set_size_after = r.scard(whitelist_set_key)
        print(f"白名单集合大小: {set_size_after}")
        print(f"MAC地址在集合中: {is_in_set_after}")
        
        # 验证修复结果
        if not is_in_set_after and set_size_after == 0:
            print("\n✅ 修复成功！Redis缓存已清理")
        else:
            print("\n⚠️ 修复可能不完整，请检查")
        
        r.close()
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")


def main():
    """主函数"""
    print("Redis缓存修复工具")
    print("=" * 50)
    
    # 确认操作
    confirm = input(f"确认要清理MAC地址 {MAC_ADDRESS} 的Redis缓存吗？(y/N): ")
    if confirm.lower() != 'y':
        print("操作已取消")
        return
    
    # 执行修复
    fix_redis_cache()
    
    print("\n" + "=" * 50)
    print("修复完成！")
    print("建议重新测试设备连接，应该会看到认证失败的消息。")


if __name__ == "__main__":
    main()
