#!/usr/bin/env python3
"""
测试文本消息触发绑定检查
"""
import asyncio
import websockets
import json
import time

async def test_text_message():
    """测试发送文本消息触发绑定检查"""
    print("测试文本消息触发绑定检查")
    print("=" * 60)
    print("测试MAC地址: FF:FF:FF:FF:FF:FF")
    print("=" * 50)

    uri = "ws://127.0.0.1:8100"
    headers = {
        'device-id': 'FF:FF:FF:FF:FF:FF',
        'client-id': 'test_client'
    }

    try:
        print(f"正在连接 {uri}...")
        print(f"Headers: {headers}")

        async with websockets.connect(uri, extra_headers=headers) as websocket:
            print("✅ WebSocket连接成功")

            # 等待欢迎消息
            try:
                welcome_msg = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                print(f"📨 收到欢迎消息: {welcome_msg}")

                # 发送文本消息 - 使用正确的格式
                print("📝 发送文本消息...")
                text_message = {
                    "type": "listen",
                    "state": "detect",
                    "text": "你好"
                }
                await websocket.send(json.dumps(text_message))

                # 等待响应消息
                print("⏳ 等待播报消息...")
                start_time = time.time()

                while time.time() - start_time < 15:  # 等待15秒
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                        print(f"📨 收到消息: {message}")

                        # 解析JSON消息
                        try:
                            msg_data = json.loads(message)
                            if msg_data.get("type") == "tts":
                                content = msg_data.get("content", "")
                                print(f"🔊 TTS播报内容: {content}")

                                # 检查是否包含预期的MAC认证提示
                                if "设备未激活" in content and "官网激活" in content:
                                    print("✅ 成功！播报了正确的MAC认证提示信息")
                                    return True
                                elif "绑定码" in content:
                                    print("❌ 错误！播报了Token认证的绑定码信息")
                                    return False
                                else:
                                    print(f"ℹ️  其他TTS内容: {content}")
                            elif msg_data.get("type") == "stt":
                                content = msg_data.get("content", "")
                                print(f"📝 STT内容: {content}")
                        except json.JSONDecodeError:
                            print(f"📄 非JSON消息: {message}")

                    except asyncio.TimeoutError:
                        print("⏳ 等待消息超时，继续等待...")
                        continue

                print("⚠️  未收到预期的播报消息")
                return False

            except asyncio.TimeoutError:
                print("❌ 等待欢迎消息超时")
                return False

    except websockets.exceptions.ConnectionClosed as e:
        print(f"❌ 连接被关闭: {e}")
        return False
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

async def main():
    success = await test_text_message()

    print("\n" + "=" * 60)
    print("测试完成！")

    if success:
        print("\n✅ 测试通过！")
    else:
        print("\n❌ 测试失败！")

if __name__ == "__main__":
    asyncio.run(main())
