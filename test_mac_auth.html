<!DOCTYPE html>
<html>
<head>
    <title>MAC地址认证测试</title>
    <meta charset="UTF-8">
</head>
<body>
    <h1>MAC地址认证测试</h1>
    <div>
        <label>MAC地址: </label>
        <input type="text" id="macAddress" value="aa:bb:cc:dd:ee:ff" />
        <button onclick="testMacAuth()">测试MAC认证</button>
    </div>
    <div>
        <label>Token: </label>
        <input type="text" id="token" value="" placeholder="留空测试MAC认证" />
        <button onclick="testTokenAuth()">测试Token认证</button>
    </div>
    <div>
        <h3>连接状态:</h3>
        <div id="status">未连接</div>
    </div>
    <div>
        <h3>日志:</h3>
        <div id="log" style="border: 1px solid #ccc; height: 300px; overflow-y: scroll; padding: 10px;"></div>
    </div>

    <script>
        let ws = null;

        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${time}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateStatus(status) {
            document.getElementById('status').textContent = status;
        }

        function testMacAuth() {
            const macAddress = document.getElementById('macAddress').value;
            if (!macAddress) {
                alert('请输入MAC地址');
                return;
            }

            log(`开始测试MAC地址认证: ${macAddress}`);
            connectWebSocket(macAddress, '');
        }

        function testTokenAuth() {
            const token = document.getElementById('token').value;
            const macAddress = document.getElementById('macAddress').value;

            if (!token) {
                alert('请输入Token');
                return;
            }

            log(`开始测试Token认证: ${token}`);
            connectWebSocket(macAddress, token);
        }

        function connectWebSocket(macAddress, token) {
            if (ws) {
                ws.close();
            }

            // 构建WebSocket URL，通过URL参数传递认证信息
            const baseUrl = 'ws://localhost:8100/xiaozhi/v1/';
            let wsUrl = `${baseUrl}?device-id=${encodeURIComponent(macAddress)}&client-id=${encodeURIComponent(macAddress)}`;

            if (token) {
                wsUrl += `&authorization=${encodeURIComponent('Bearer ' + token)}`;
            }

            log(`连接WebSocket: ${wsUrl}`);
            ws = new WebSocket(wsUrl);

            ws.onopen = function(event) {
                log('WebSocket连接已建立');
                updateStatus('已连接');

                // 发送测试消息
                const testMessage = {
                    type: 'text',
                    data: '你好，这是一个测试消息'
                };
                ws.send(JSON.stringify(testMessage));
                log('发送测试消息: ' + JSON.stringify(testMessage));
            };

            ws.onmessage = function(event) {
                log('收到消息: ' + event.data);
            };

            ws.onclose = function(event) {
                log(`WebSocket连接已关闭: code=${event.code}, reason=${event.reason}`);
                updateStatus('已断开');
            };

            ws.onerror = function(error) {
                log('WebSocket错误: ' + error);
                updateStatus('连接错误');
            };
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
    </script>

    <button onclick="clearLog()">清空日志</button>
</body>
</html>
