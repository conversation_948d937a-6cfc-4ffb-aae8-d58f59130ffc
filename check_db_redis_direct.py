#!/usr/bin/env python3
"""
直接连接数据库和Redis检查MAC地址状态
"""

import asyncio
import aiomysql
import redis
import json

# 配置信息
MAC_ADDRESS = "C9:49:85:69:83:33"

# 数据库配置（从application-dev.yml获取）
DB_CONFIG = {
    'host': '127.0.0.1',
    'port': 3306,
    'user': 'root',
    'password': 'Siming@11',
    'db': 'xiaozhi_esp32_server',
    'charset': 'utf8mb4'
}

# Redis配置（从application-dev.yml获取）
REDIS_CONFIG = {
    'host': '127.0.0.1',
    'port': 6379,
    'db': 0,
    'password': None  # 配置文件中为空
}


async def check_mysql_database():
    """检查MySQL数据库中的MAC地址状态"""
    print("=== 检查MySQL数据库 ===")

    try:
        # 连接数据库
        conn = await aiomysql.connect(**DB_CONFIG)
        cursor = await conn.cursor()

        # 检查白名单表
        print(f"\n1. 检查白名单表 (ai_mac_whitelist):")
        sql = "SELECT * FROM ai_mac_whitelist WHERE mac_address = %s"
        await cursor.execute(sql, (MAC_ADDRESS,))
        whitelist_result = await cursor.fetchall()

        if whitelist_result:
            print(f"   找到记录: {len(whitelist_result)} 条")
            for row in whitelist_result:
                print(f"   记录: {row}")
        else:
            print(f"   ❌ 未找到MAC地址 {MAC_ADDRESS} 的记录")

        # 检查黑名单表
        print(f"\n2. 检查黑名单表 (ai_mac_blacklist):")
        sql = "SELECT * FROM ai_mac_blacklist WHERE mac_address = %s"
        await cursor.execute(sql, (MAC_ADDRESS,))
        blacklist_result = await cursor.fetchall()

        if blacklist_result:
            print(f"   找到记录: {len(blacklist_result)} 条")
            for row in blacklist_result:
                print(f"   记录: {row}")
        else:
            print(f"   ❌ 未找到MAC地址 {MAC_ADDRESS} 的记录")

        # 检查所有启用的MAC地址
        print(f"\n3. 检查所有启用的MAC地址:")
        sql = "SELECT mac_address, status, create_date FROM ai_mac_whitelist WHERE status = 1 ORDER BY create_date DESC LIMIT 10"
        await cursor.execute(sql)
        enabled_macs = await cursor.fetchall()

        print(f"   启用的MAC地址总数: {len(enabled_macs)}")
        for row in enabled_macs:
            print(f"   {row}")

        # 检查表结构
        print(f"\n4. 检查白名单表结构:")
        sql = "DESCRIBE ai_mac_whitelist"
        await cursor.execute(sql)
        table_structure = await cursor.fetchall()

        for row in table_structure:
            print(f"   {row}")

        await cursor.close()
        conn.close()

        return whitelist_result, blacklist_result

    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        print("请检查数据库配置信息")
        return None, None


def check_redis_cache():
    """检查Redis缓存中的MAC地址状态"""
    print("\n=== 检查Redis缓存 ===")

    try:
        # 连接Redis
        if REDIS_CONFIG['password']:
            r = redis.Redis(
                host=REDIS_CONFIG['host'],
                port=REDIS_CONFIG['port'],
                db=REDIS_CONFIG['db'],
                password=REDIS_CONFIG['password'],
                decode_responses=True
            )
        else:
            r = redis.Redis(
                host=REDIS_CONFIG['host'],
                port=REDIS_CONFIG['port'],
                db=REDIS_CONFIG['db'],
                decode_responses=True
            )

        # 测试连接
        r.ping()

        # 检查特定MAC地址的认证缓存
        auth_key = f"mac:auth:{MAC_ADDRESS}"
        print(f"\n1. 检查MAC认证缓存 ({auth_key}):")
        auth_result = r.get(auth_key)
        if auth_result:
            print(f"   ✅ 找到缓存: {auth_result}")
        else:
            print(f"   ❌ 未找到缓存")

        # 检查白名单集合缓存
        whitelist_set_key = "mac:whitelist:set"
        print(f"\n2. 检查白名单集合缓存 ({whitelist_set_key}):")
        is_in_set = r.sismember(whitelist_set_key, MAC_ADDRESS)
        set_size = r.scard(whitelist_set_key)
        print(f"   集合大小: {set_size}")
        print(f"   MAC地址在集合中: {is_in_set}")

        if set_size > 0:
            # 获取集合中的一些成员作为示例
            sample_members = r.srandmember(whitelist_set_key, 5)
            print(f"   集合示例成员: {sample_members}")

        # 检查白名单计数缓存
        count_key = "mac:whitelist:count"
        print(f"\n3. 检查白名单计数缓存 ({count_key}):")
        count_result = r.get(count_key)
        if count_result:
            print(f"   ✅ 找到缓存: {count_result}")
        else:
            print(f"   ❌ 未找到缓存")

        # 检查所有MAC相关的键
        print(f"\n4. 检查所有MAC相关的Redis键:")
        mac_keys = r.keys("mac:*")
        print(f"   MAC相关键总数: {len(mac_keys)}")
        for key in mac_keys[:10]:  # 只显示前10个
            key_type = r.type(key)
            if key_type == 'string':
                value = r.get(key)
                print(f"   {key} ({key_type}): {value}")
            elif key_type == 'set':
                size = r.scard(key)
                print(f"   {key} ({key_type}): size={size}")
            elif key_type == 'hash':
                size = r.hlen(key)
                print(f"   {key} ({key_type}): size={size}")
            else:
                print(f"   {key} ({key_type})")

        r.close()

        return auth_result, is_in_set

    except Exception as e:
        print(f"❌ Redis连接失败: {e}")
        print("请检查Redis配置信息")
        return None, None


async def main():
    """主函数"""
    print(f"直接检查MAC地址: {MAC_ADDRESS}")
    print("=" * 60)

    # 检查数据库
    db_whitelist, db_blacklist = await check_mysql_database()

    # 检查Redis缓存
    redis_auth, redis_in_set = check_redis_cache()

    # 分析结果
    print("\n" + "=" * 60)
    print("=== 综合分析 ===")

    print(f"\n数据库状态:")
    if db_whitelist:
        print(f"  ✅ 白名单表中有记录: {len(db_whitelist)} 条")
        for row in db_whitelist:
            status = "启用" if row[3] == 1 else "禁用"  # 假设status是第4列
            print(f"     状态: {status}")
    else:
        print(f"  ❌ 白名单表中无记录")

    if db_blacklist:
        print(f"  ⚠️  黑名单表中有记录: {len(db_blacklist)} 条")
    else:
        print(f"  ✅ 黑名单表中无记录")

    print(f"\nRedis缓存状态:")
    if redis_auth:
        print(f"  ⚠️  认证缓存存在: {redis_auth}")
    else:
        print(f"  ✅ 认证缓存不存在")

    if redis_in_set:
        print(f"  ⚠️  在白名单集合中")
    else:
        print(f"  ✅ 不在白名单集合中")

    # 给出建议
    print(f"\n建议操作:")
    if redis_auth or redis_in_set:
        print("1. 清理Redis缓存:")
        print(f"   redis-cli DEL mac:auth:{MAC_ADDRESS}")
        print(f"   redis-cli DEL mac:whitelist:set")
        print(f"   redis-cli DEL mac:whitelist:count")

    if db_whitelist:
        print("2. 更新数据库:")
        print(f"   UPDATE ai_mac_whitelist SET status = 0 WHERE mac_address = '{MAC_ADDRESS}';")
        print(f"   或者")
        print(f"   DELETE FROM ai_mac_whitelist WHERE mac_address = '{MAC_ADDRESS}';")


if __name__ == "__main__":
    print("注意：请先修改脚本中的数据库和Redis连接配置")
    print("DB_CONFIG 和 REDIS_CONFIG 需要根据实际环境调整")
    print()

    # 检查是否需要安装依赖
    try:
        import aiomysql
        import redis
    except ImportError as e:
        print(f"缺少依赖包: {e}")
        print("请安装: pip install aiomysql redis")
        exit(1)

    asyncio.run(main())
